const jwt = require('jsonwebtoken');
const db = require('../config/database');

// JWT認證中間件
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: '需要提供存取權杖' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 從資料庫獲取使用者詳細資訊
    const result = await db.query(
      'SELECT id, username, name, role, class_name, avatar_url FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ error: '無效的權杖' });
    }

    req.user = result.rows[0];
    next();
  } catch (error) {
    console.error('JWT驗證錯誤:', error);
    return res.status(403).json({ error: '權杖無效或已過期' });
  }
};

// 角色權限驗證中間件
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: '需要先驗證身份' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: '權限不足' });
    }

    next();
  };
};

// 生成JWT Token
const generateToken = (userId, role) => {
  return jwt.sign(
    { userId, role },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// 生成重置密碼Token
const generateResetToken = (userId) => {
  return jwt.sign(
    { userId, type: 'reset' },
    process.env.JWT_SECRET,
    { expiresIn: '1h' }
  );
};

module.exports = {
  authenticateToken,
  requireRole,
  generateToken,
  generateResetToken
};