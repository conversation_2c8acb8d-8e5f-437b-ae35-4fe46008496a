const db = require('../config/database');

// 獲取使用者的所有精靈
const getUserSpirits = async (req, res) => {
  try {
    const userId = req.user.role === 'student' ? req.user.id : req.params.userId;
    
    // 檢查權限：學生只能查看自己的精靈
    if (req.user.role === 'student' && userId !== req.user.id) {
      return res.status(403).json({ error: '權限不足' });
    }

    const result = await db.query(
      `SELECT us.id, us.level, us.current_exp, us.total_exp, us.acquired_at,
              st.name, st.type_code, st.description, st.image_url
       FROM user_spirits us
       JOIN spirit_types st ON us.spirit_type_id = st.id
       WHERE us.user_id = $1
       ORDER BY st.type_code`,
      [userId]
    );

    // 獲取每個精靈的等級進度
    const spirits = result.rows.map(spirit => {
      const expPerLevel = 100; // 可從系統設定獲取
      const currentLevelExp = spirit.current_exp % expPerLevel;
      const nextLevelExp = expPerLevel;
      const progress = (currentLevelExp / nextLevelExp) * 100;

      return {
        ...spirit,
        current_level_exp: currentLevelExp,
        next_level_exp: nextLevelExp,
        progress: Math.round(progress)
      };
    });

    res.json({ spirits });

  } catch (error) {
    console.error('獲取使用者精靈錯誤:', error);
    res.status(500).json({ error: '獲取精靈資訊失敗' });
  }
};

// 獲取特定精靈詳細資訊
const getSpiritDetails = async (req, res) => {
  try {
    const { spiritId } = req.params;
    const userId = req.user.id;

    const result = await db.query(
      `SELECT us.id, us.level, us.current_exp, us.total_exp, us.acquired_at,
              st.name, st.type_code, st.description, st.image_url
       FROM user_spirits us
       JOIN spirit_types st ON us.spirit_type_id = st.id
       WHERE us.id = $1 AND us.user_id = $2`,
      [spiritId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: '找不到該精靈' });
    }

    const spirit = result.rows[0];

    // 計算等級進度
    const expPerLevel = 100;
    const currentLevelExp = spirit.current_exp % expPerLevel;
    const nextLevelExp = expPerLevel;
    const progress = (currentLevelExp / nextLevelExp) * 100;

    // 獲取該精靈相關的任務統計
    const taskStats = await db.query(
      `SELECT COUNT(*) as total_tasks,
              COUNT(CASE WHEN t.status = 'approved' THEN 1 END) as completed_tasks
       FROM tasks t
       JOIN task_types tt ON t.task_type_id = tt.id
       WHERE t.student_id = $1 AND tt.spirit_type = $2`,
      [userId, spirit.type_code]
    );

    const spiritDetails = {
      ...spirit,
      current_level_exp: currentLevelExp,
      next_level_exp: nextLevelExp,
      progress: Math.round(progress),
      task_stats: taskStats.rows[0]
    };

    res.json({ spirit: spiritDetails });

  } catch (error) {
    console.error('獲取精靈詳細資訊錯誤:', error);
    res.status(500).json({ error: '獲取精靈詳細資訊失敗' });
  }
};

// 獲取所有精靈類型
const getSpiritTypes = async (req, res) => {
  try {
    const result = await db.query(
      'SELECT id, type_code, name, description, image_url FROM spirit_types ORDER BY type_code'
    );

    res.json({ spiritTypes: result.rows });

  } catch (error) {
    console.error('獲取精靈類型錯誤:', error);
    res.status(500).json({ error: '獲取精靈類型失敗' });
  }
};

// 獲取精靈收集圖鑑
const getSpiritCollection = async (req, res) => {
  try {
    const userId = req.user.id;

    // 獲取所有精靈類型和使用者精靈資訊
    const result = await db.query(
      `SELECT st.id, st.type_code, st.name, st.description, st.image_url,
              us.level, us.current_exp, us.total_exp, us.acquired_at,
              CASE WHEN us.id IS NOT NULL THEN true ELSE false END as is_collected
       FROM spirit_types st
       LEFT JOIN user_spirits us ON st.id = us.spirit_type_id AND us.user_id = $1
       ORDER BY st.type_code`,
      [userId]
    );

    const collection = result.rows.map(item => {
      if (item.is_collected) {
        const expPerLevel = 100;
        const currentLevelExp = item.current_exp % expPerLevel;
        const progress = (currentLevelExp / expPerLevel) * 100;

        return {
          id: item.id,
          type_code: item.type_code,
          name: item.name,
          description: item.description,
          image_url: item.image_url,
          is_collected: true,
          level: item.level,
          current_exp: item.current_exp,
          total_exp: item.total_exp,
          progress: Math.round(progress),
          acquired_at: item.acquired_at
        };
      } else {
        return {
          id: item.id,
          type_code: item.type_code,
          name: item.name,
          description: item.description,
          image_url: item.image_url,
          is_collected: false
        };
      }
    });

    // 計算收集統計
    const totalSpirits = collection.length;
    const collectedSpirits = collection.filter(s => s.is_collected).length;
    const collectionRate = Math.round((collectedSpirits / totalSpirits) * 100);

    res.json({
      collection,
      stats: {
        total: totalSpirits,
        collected: collectedSpirits,
        collection_rate: collectionRate
      }
    });

  } catch (error) {
    console.error('獲取精靈收集圖鑑錯誤:', error);
    res.status(500).json({ error: '獲取精靈收集圖鑑失敗' });
  }
};

// 獲取精靈排行榜
const getSpiritLeaderboard = async (req, res) => {
  try {
    const { spirit_type, limit = 50 } = req.query;

    let whereClause = '';
    let queryParams = [parseInt(limit)];

    if (spirit_type) {
      whereClause = 'WHERE st.type_code = $2';
      queryParams.push(spirit_type);
    }

    const result = await db.query(
      `SELECT u.name, u.class_name, u.avatar_url,
              us.level, us.total_exp,
              st.name as spirit_name, st.type_code
       FROM user_spirits us
       JOIN users u ON us.user_id = u.id
       JOIN spirit_types st ON us.spirit_type_id = st.id
       ${whereClause}
       ORDER BY us.level DESC, us.total_exp DESC
       LIMIT $1`,
      queryParams
    );

    const leaderboard = result.rows.map((item, index) => ({
      rank: index + 1,
      student_name: item.name,
      class_name: item.class_name,
      avatar_url: item.avatar_url,
      spirit_name: item.spirit_name,
      spirit_type: item.type_code,
      level: item.level,
      total_exp: item.total_exp
    }));

    res.json({ leaderboard });

  } catch (error) {
    console.error('獲取精靈排行榜錯誤:', error);
    res.status(500).json({ error: '獲取精靈排行榜失敗' });
  }
};

// 獲取班級精靈統計
const getClassSpiritStats = async (req, res) => {
  try {
    const { class_name } = req.query;
    
    if (!class_name && req.user.role !== 'admin') {
      return res.status(400).json({ error: '請指定班級名稱' });
    }

    let whereClause = "WHERE u.role = 'student'";
    let queryParams = [];

    if (class_name) {
      whereClause += " AND u.class_name = $1";
      queryParams.push(class_name);
    }

    // 獲取班級精靈等級分布
    const levelDistribution = await db.query(
      `SELECT st.name as spirit_name, st.type_code,
              us.level, COUNT(*) as student_count
       FROM user_spirits us
       JOIN users u ON us.user_id = u.id
       JOIN spirit_types st ON us.spirit_type_id = st.id
       ${whereClause}
       GROUP BY st.name, st.type_code, us.level
       ORDER BY st.type_code, us.level`,
      queryParams
    );

    // 獲取班級平均等級
    const avgLevels = await db.query(
      `SELECT st.name as spirit_name, st.type_code,
              ROUND(AVG(us.level), 2) as avg_level,
              ROUND(AVG(us.total_exp), 0) as avg_exp
       FROM user_spirits us
       JOIN users u ON us.user_id = u.id
       JOIN spirit_types st ON us.spirit_type_id = st.id
       ${whereClause}
       GROUP BY st.name, st.type_code
       ORDER BY st.type_code`,
      queryParams
    );

    res.json({
      level_distribution: levelDistribution.rows,
      average_levels: avgLevels.rows
    });

  } catch (error) {
    console.error('獲取班級精靈統計錯誤:', error);
    res.status(500).json({ error: '獲取班級精靈統計失敗' });
  }
};

module.exports = {
  getUserSpirits,
  getSpiritDetails,
  getSpiritTypes,
  getSpiritCollection,
  getSpiritLeaderboard,
  getClassSpiritStats
};