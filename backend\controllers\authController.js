const bcrypt = require('bcryptjs');
const { validationResult } = require('express-validator');
const db = require('../config/database');
const { generateToken, generateResetToken } = require('../middleware/auth');

// 使用者註冊
const register = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '輸入資料有誤',
        details: errors.array()
      });
    }

    const { username, password, name, email, class_name, role = 'student' } = req.body;

    // 檢查使用者名稱是否已存在
    const existingUser = await db.query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: '使用者名稱或電子郵件已存在' });
    }

    // 加密密碼
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 建立新使用者
    const result = await db.query(
      `INSERT INTO users (username, password_hash, name, email, class_name, role) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING id, username, name, role, class_name, created_at`,
      [username, passwordHash, name, email, class_name, role]
    );

    const newUser = result.rows[0];

    // 為學生創建初始精靈
    if (role === 'student') {
      await initializeUserSpirits(newUser.id);
    }

    // 生成JWT Token
    const token = generateToken(newUser.id, newUser.role);

    res.status(201).json({
      message: '註冊成功',
      token,
      user: {
        id: newUser.id,
        username: newUser.username,
        name: newUser.name,
        role: newUser.role,
        class_name: newUser.class_name
      }
    });

  } catch (error) {
    console.error('註冊錯誤:', error);
    res.status(500).json({ error: '註冊失敗，請稍後再試' });
  }
};

// 使用者登入
const login = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '輸入資料有誤',
        details: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查找使用者
    const result = await db.query(
      'SELECT id, username, password_hash, name, role, class_name, avatar_url FROM users WHERE username = $1',
      [username]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ error: '使用者名稱或密碼錯誤' });
    }

    const user = result.rows[0];

    // 驗證密碼
    const passwordValid = await bcrypt.compare(password, user.password_hash);
    if (!passwordValid) {
      return res.status(401).json({ error: '使用者名稱或密碼錯誤' });
    }

    // 生成JWT Token
    const token = generateToken(user.id, user.role);

    res.json({
      message: '登入成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        class_name: user.class_name,
        avatar_url: user.avatar_url
      }
    });

  } catch (error) {
    console.error('登入錯誤:', error);
    res.status(500).json({ error: '登入失敗，請稍後再試' });
  }
};

// 獲取使用者資訊
const getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await db.query(
      `SELECT id, username, name, email, role, class_name, avatar_url, created_at 
       FROM users WHERE id = $1`,
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: '找不到使用者' });
    }

    const user = result.rows[0];

    // 如果是學生，獲取精靈資訊
    if (user.role === 'student') {
      const spiritsResult = await db.query(
        `SELECT us.level, us.current_exp, us.total_exp, st.name, st.type_code, st.image_url
         FROM user_spirits us
         JOIN spirit_types st ON us.spirit_type_id = st.id
         WHERE us.user_id = $1`,
        [userId]
      );
      user.spirits = spiritsResult.rows;
    }

    res.json({ user });

  } catch (error) {
    console.error('獲取個人資料錯誤:', error);
    res.status(500).json({ error: '獲取個人資料失敗' });
  }
};

// 更新使用者資訊
const updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '輸入資料有誤',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const { name, email, class_name } = req.body;

    const result = await db.query(
      `UPDATE users SET name = $1, email = $2, class_name = $3, updated_at = CURRENT_TIMESTAMP
       WHERE id = $4 
       RETURNING id, username, name, email, role, class_name, avatar_url`,
      [name, email, class_name, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: '找不到使用者' });
    }

    res.json({
      message: '個人資料更新成功',
      user: result.rows[0]
    });

  } catch (error) {
    console.error('更新個人資料錯誤:', error);
    if (error.code === '23505') { // PostgreSQL unique violation
      res.status(400).json({ error: '電子郵件已被使用' });
    } else {
      res.status(500).json({ error: '更新個人資料失敗' });
    }
  }
};

// 修改密碼
const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '輸入資料有誤',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // 獲取當前密碼雜湊
    const result = await db.query(
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: '找不到使用者' });
    }

    // 驗證當前密碼
    const passwordValid = await bcrypt.compare(currentPassword, result.rows[0].password_hash);
    if (!passwordValid) {
      return res.status(400).json({ error: '當前密碼錯誤' });
    }

    // 加密新密碼
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密碼
    await db.query(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [newPasswordHash, userId]
    );

    res.json({ message: '密碼修改成功' });

  } catch (error) {
    console.error('修改密碼錯誤:', error);
    res.status(500).json({ error: '修改密碼失敗' });
  }
};

// 初始化使用者精靈（學生註冊時調用）
const initializeUserSpirits = async (userId) => {
  try {
    const spiritTypes = await db.query('SELECT id FROM spirit_types');
    
    for (const spiritType of spiritTypes.rows) {
      await db.query(
        'INSERT INTO user_spirits (user_id, spirit_type_id) VALUES ($1, $2)',
        [userId, spiritType.id]
      );
    }
  } catch (error) {
    console.error('初始化使用者精靈錯誤:', error);
    throw error;
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword
};