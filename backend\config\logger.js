const winston = require('winston');
const path = require('path');
const fs = require('fs');

// 確保日誌目錄存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 根據環境決定日誌檔案名稱和路徑
const getLogFileName = () => {
  const env = process.env.LOG_ENV || 'dev';
  const now = new Date();
  
  if (env === 'dev') {
    return path.join(__dirname, '../app.log');
  } else {
    const timestamp = now.toISOString().replace(/[:.]/g, '-').split('T');
    const date = timestamp[0];
    const time = timestamp[1].split('.')[0];
    return path.join(logDir, `reading-game_${date}_${time}.log`);
  }
};

// 自定義日誌格式
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, module = 'SYSTEM', event = 'GENERAL', statusCode = 200, ...meta }) => {
    const details = Object.keys(meta).length > 0 ? JSON.stringify(meta) : '';
    return `${timestamp}-${module.toUpperCase()}-${level.toUpperCase()}-${event}-${statusCode}-${message}${details ? ' ' + details : ''}`;
  })
);

// 建立logger實例
const logger = winston.createLogger({
  level: process.env.LOG_ENV === 'dev' ? 'debug' : 'info',
  format: customFormat,
  transports: [
    // 檔案輸出
    new winston.transports.File({ 
      filename: getLogFileName(),
      options: { flags: process.env.LOG_ENV === 'dev' ? 'w' : 'a' } // 開發環境清空重寫，生產環境追加
    })
  ]
});

// 開發環境同時輸出到控制台
if (process.env.LOG_ENV === 'dev') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.printf(({ timestamp, level, message, module = 'SYSTEM' }) => {
        return `${timestamp} [${module}] ${level}: ${message}`;
      })
    )
  }));
}

// 清空開發環境日誌檔案
if (process.env.LOG_ENV === 'dev') {
  const devLogFile = path.join(__dirname, '../app.log');
  if (fs.existsSync(devLogFile)) {
    fs.writeFileSync(devLogFile, '');
  }
}

// 封裝日誌方法
const log = {
  debug: (message, options = {}) => {
    logger.debug(message, options);
  },
  
  info: (message, options = {}) => {
    logger.info(message, options);
  },
  
  warn: (message, options = {}) => {
    logger.warn(message, options);
  },
  
  error: (message, options = {}) => {
    logger.error(message, options);
  },

  // 特定業務日誌方法
  auth: (message, statusCode = 200, userId = null) => {
    logger.info(message, { 
      module: 'AUTH', 
      event: 'LOGIN', 
      statusCode,
      userId 
    });
  },

  task: (message, statusCode = 200, userId = null, taskId = null) => {
    logger.info(message, { 
      module: 'TASK', 
      event: 'TASK_ACTION', 
      statusCode,
      userId,
      taskId 
    });
  },

  spirit: (message, statusCode = 200, userId = null, spiritId = null) => {
    logger.info(message, { 
      module: 'SPIRIT', 
      event: 'SPIRIT_ACTION', 
      statusCode,
      userId,
      spiritId 
    });
  },

  api: (method, url, statusCode, userId = null, responseTime = null) => {
    logger.info(`${method} ${url}`, {
      module: 'API',
      event: 'REQUEST',
      statusCode,
      userId,
      responseTime
    });
  }
};

module.exports = log;