const log = require('../config/logger');

// API請求日誌中間件
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // 記錄請求開始
  const userId = req.user ? req.user.id : null;
  
  // 覆寫res.end來記錄響應
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // 記錄API請求日誌
    log.api(req.method, req.originalUrl, res.statusCode, userId, responseTime);
    
    // 調用原始的end方法
    originalEnd.call(res, chunk, encoding);
  };

  next();
};

// 錯誤日誌中間件
const errorLogger = (err, req, res, next) => {
  const userId = req.user ? req.user.id : null;
  
  // 記錄錯誤日誌
  log.error(`${req.method} ${req.originalUrl} - ${err.message}`, {
    module: 'ERROR',
    event: 'UNHANDLED_ERROR',
    statusCode: err.status || 500,
    userId,
    stack: err.stack,
    body: req.body
  });

  // 傳遞錯誤到下一個中間件
  next(err);
};

// 資料庫錯誤日誌
const dbErrorLogger = (error, query, params) => {
  log.error(`Database Error: ${error.message}`, {
    module: 'DATABASE',
    event: 'QUERY_ERROR',
    statusCode: 500,
    query,
    params
  });
};

// 業務邏輯日誌記錄器
const businessLogger = {
  userRegistered: (userId, username) => {
    log.auth(`使用者註冊成功: ${username}`, 201, userId);
  },

  userLogin: (userId, username) => {
    log.auth(`使用者登入: ${username}`, 200, userId);
  },
  
  userLoginFailed: (username, reason) => {
    log.auth(`登入失敗: ${username} - ${reason}`, 401);
  },

  taskCreated: (userId, taskId, taskType) => {
    log.task(`建立任務: ${taskType}`, 201, userId, taskId);
  },

  taskSubmitted: (userId, taskId) => {
    log.task(`提交任務`, 200, userId, taskId);
  },

  taskApproved: (teacherId, taskId, studentId) => {
    log.task(`任務審核通過`, 200, teacherId, taskId, { studentId });
  },

  taskRejected: (teacherId, taskId, studentId, reason) => {
    log.task(`任務審核退回: ${reason}`, 200, teacherId, taskId, { studentId });
  },

  spiritLevelUp: (userId, spiritType, newLevel) => {
    log.spirit(`精靈升級: ${spiritType} -> Lv.${newLevel}`, 200, userId);
  },

  fileUploaded: (userId, fileName, fileSize) => {
    log.info(`檔案上傳: ${fileName}`, {
      module: 'FILE',
      event: 'UPLOAD',
      statusCode: 200,
      userId,
      fileName,
      fileSize
    });
  },

  fileUploadFailed: (userId, fileName, error) => {
    log.error(`檔案上傳失敗: ${fileName} - ${error}`, {
      module: 'FILE',
      event: 'UPLOAD_FAILED',
      statusCode: 400,
      userId,
      fileName
    });
  }
};

module.exports = {
  requestLogger,
  errorLogger,
  dbErrorLogger,
  businessLogger
};