const fs = require('fs');
const path = require('path');
const db = require('../config/database');

async function initializeDatabase() {
  try {
    console.log('🔄 開始初始化資料庫...');
    
    // 讀取SQL初始化腳本
    const sqlScript = fs.readFileSync(path.join(__dirname, 'init.sql'), 'utf8');
    
    // 執行SQL腳本
    await db.query(sqlScript);
    
    console.log('✅ 資料庫初始化完成');
    console.log('📊 已建立以下表格：');
    console.log('   - users (使用者表)');
    console.log('   - task_types (任務類型表)');
    console.log('   - tasks (任務表)');
    console.log('   - spirit_types (精靈類型表)');
    console.log('   - user_spirits (使用者精靈表)');
    console.log('   - task_files (任務檔案表)');
    console.log('   - system_settings (系統設定表)');
    
  } catch (error) {
    console.error('❌ 資料庫初始化失敗:', error);
    throw error;
  }
}

// 如果直接執行此檔案，則初始化資料庫
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('🎉 資料庫初始化成功！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 初始化過程發生錯誤：', error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase };