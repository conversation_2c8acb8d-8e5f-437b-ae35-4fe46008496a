const { validationResult } = require('express-validator');
const db = require('../config/database');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

// 設定檔案上傳
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/tasks');
    try {
      await fs.access(uploadDir);
    } catch {
      await fs.mkdir(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `task-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允許上傳圖片、PDF或Word文檔'));
    }
  }
});

// 獲取所有任務類型
const getTaskTypes = async (req, res) => {
  try {
    const result = await db.query(
      'SELECT id, type_code, name, description, spirit_type, exp_reward FROM task_types ORDER BY type_code'
    );

    res.json({ taskTypes: result.rows });

  } catch (error) {
    console.error('獲取任務類型錯誤:', error);
    res.status(500).json({ error: '獲取任務類型失敗' });
  }
};

// 創建新任務
const createTask = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '輸入資料有誤',
        details: errors.array()
      });
    }

    const { title, description, task_type_id, content = {} } = req.body;
    const student_id = req.user.id;

    // 檢查任務類型是否存在
    const taskTypeResult = await db.query(
      'SELECT id, name FROM task_types WHERE id = $1',
      [task_type_id]
    );

    if (taskTypeResult.rows.length === 0) {
      return res.status(400).json({ error: '無效的任務類型' });
    }

    // 創建任務
    const result = await db.query(
      `INSERT INTO tasks (title, description, task_type_id, student_id, content, status)
       VALUES ($1, $2, $3, $4, $5, 'pending')
       RETURNING id, title, description, content, status, created_at`,
      [title, description, task_type_id, student_id, JSON.stringify(content)]
    );

    const newTask = result.rows[0];

    res.status(201).json({
      message: '任務創建成功',
      task: {
        id: newTask.id,
        title: newTask.title,
        description: newTask.description,
        task_type: taskTypeResult.rows[0].name,
        content: newTask.content,
        status: newTask.status,
        created_at: newTask.created_at
      }
    });

  } catch (error) {
    console.error('創建任務錯誤:', error);
    res.status(500).json({ error: '創建任務失敗' });
  }
};

// 提交任務
const submitTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const student_id = req.user.id;

    // 檢查任務是否存在且屬於該學生
    const taskResult = await db.query(
      'SELECT id, status FROM tasks WHERE id = $1 AND student_id = $2',
      [id, student_id]
    );

    if (taskResult.rows.length === 0) {
      return res.status(404).json({ error: '找不到該任務' });
    }

    const task = taskResult.rows[0];

    if (task.status !== 'pending') {
      return res.status(400).json({ error: '該任務已經提交或完成' });
    }

    // 更新任務狀態為已提交
    const result = await db.query(
      `UPDATE tasks 
       SET content = $1, status = 'submitted', submitted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND student_id = $3
       RETURNING id, title, content, status, submitted_at`,
      [JSON.stringify(content), id, student_id]
    );

    res.json({
      message: '任務提交成功',
      task: result.rows[0]
    });

  } catch (error) {
    console.error('提交任務錯誤:', error);
    res.status(500).json({ error: '提交任務失敗' });
  }
};

// 獲取學生的任務列表
const getStudentTasks = async (req, res) => {
  try {
    const student_id = req.user.role === 'student' ? req.user.id : req.query.student_id;
    const { status, task_type, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE t.student_id = $1';
    let queryParams = [student_id, parseInt(limit), offset];
    let paramIndex = 4;

    if (status) {
      whereClause += ` AND t.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    if (task_type) {
      whereClause += ` AND tt.type_code = $${paramIndex}`;
      queryParams.push(task_type);
      paramIndex++;
    }

    const result = await db.query(
      `SELECT t.id, t.title, t.description, t.content, t.status, t.teacher_comment, 
              t.teacher_rating, t.submitted_at, t.reviewed_at, t.created_at,
              tt.name as task_type_name, tt.type_code, tt.exp_reward,
              u.name as teacher_name
       FROM tasks t
       JOIN task_types tt ON t.task_type_id = tt.id
       LEFT JOIN users u ON t.teacher_id = u.id
       ${whereClause}
       ORDER BY t.created_at DESC
       LIMIT $2 OFFSET $3`,
      queryParams
    );

    // 獲取總數
    const countParams = queryParams.slice(0, paramIndex - 2);
    const countResult = await db.query(
      `SELECT COUNT(*) FROM tasks t
       JOIN task_types tt ON t.task_type_id = tt.id
       ${whereClause}`,
      countParams
    );

    res.json({
      tasks: result.rows,
      total: parseInt(countResult.rows[0].count),
      page: parseInt(page),
      limit: parseInt(limit)
    });

  } catch (error) {
    console.error('獲取學生任務列表錯誤:', error);
    res.status(500).json({ error: '獲取任務列表失敗' });
  }
};

// 獲取任務詳細資訊
const getTaskDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await db.query(
      `SELECT t.id, t.title, t.description, t.content, t.status, t.teacher_comment,
              t.teacher_rating, t.submitted_at, t.reviewed_at, t.created_at,
              tt.name as task_type_name, tt.type_code, tt.description as task_type_description,
              tt.exp_reward, tt.spirit_type,
              s.name as student_name, s.class_name,
              teacher.name as teacher_name
       FROM tasks t
       JOIN task_types tt ON t.task_type_id = tt.id
       JOIN users s ON t.student_id = s.id
       LEFT JOIN users teacher ON t.teacher_id = teacher.id
       WHERE t.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: '找不到該任務' });
    }

    const task = result.rows[0];

    // 檢查權限：學生只能查看自己的任務
    if (req.user.role === 'student' && task.student_id !== req.user.id) {
      return res.status(403).json({ error: '權限不足' });
    }

    // 獲取任務相關檔案
    const filesResult = await db.query(
      'SELECT id, file_name, file_path, file_type, file_size, uploaded_at FROM task_files WHERE task_id = $1',
      [id]
    );

    task.files = filesResult.rows;

    res.json({ task });

  } catch (error) {
    console.error('獲取任務詳細資訊錯誤:', error);
    res.status(500).json({ error: '獲取任務詳細資訊失敗' });
  }
};

// 上傳任務檔案
const uploadTaskFile = [
  upload.single('file'),
  async (req, res) => {
    try {
      const { taskId } = req.params;
      
      if (!req.file) {
        return res.status(400).json({ error: '請選擇要上傳的檔案' });
      }

      // 檢查任務是否存在且屬於該學生
      const taskResult = await db.query(
        'SELECT id, student_id FROM tasks WHERE id = $1',
        [taskId]
      );

      if (taskResult.rows.length === 0) {
        return res.status(404).json({ error: '找不到該任務' });
      }

      if (req.user.role === 'student' && taskResult.rows[0].student_id !== req.user.id) {
        return res.status(403).json({ error: '權限不足' });
      }

      // 儲存檔案資訊到資料庫
      const result = await db.query(
        `INSERT INTO task_files (task_id, file_name, file_path, file_type, file_size)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id, file_name, file_type, file_size, uploaded_at`,
        [
          taskId,
          req.file.originalname,
          req.file.path,
          req.file.mimetype,
          req.file.size
        ]
      );

      res.json({
        message: '檔案上傳成功',
        file: result.rows[0]
      });

    } catch (error) {
      console.error('上傳任務檔案錯誤:', error);
      res.status(500).json({ error: '檔案上傳失敗' });
    }
  }
];

// 刪除任務檔案
const deleteTaskFile = async (req, res) => {
  try {
    const { taskId, fileId } = req.params;

    // 檢查檔案是否存在
    const fileResult = await db.query(
      `SELECT tf.id, tf.file_path, t.student_id
       FROM task_files tf
       JOIN tasks t ON tf.task_id = t.id
       WHERE tf.id = $1 AND tf.task_id = $2`,
      [fileId, taskId]
    );

    if (fileResult.rows.length === 0) {
      return res.status(404).json({ error: '找不到該檔案' });
    }

    const file = fileResult.rows[0];

    // 檢查權限
    if (req.user.role === 'student' && file.student_id !== req.user.id) {
      return res.status(403).json({ error: '權限不足' });
    }

    // 刪除實體檔案
    try {
      await fs.unlink(file.file_path);
    } catch (error) {
      console.warn('刪除實體檔案失敗:', error);
    }

    // 從資料庫刪除檔案記錄
    await db.query('DELETE FROM task_files WHERE id = $1', [fileId]);

    res.json({ message: '檔案刪除成功' });

  } catch (error) {
    console.error('刪除任務檔案錯誤:', error);
    res.status(500).json({ error: '檔案刪除失敗' });
  }
};

module.exports = {
  getTaskTypes,
  createTask,
  submitTask,
  getStudentTasks,
  getTaskDetails,
  uploadTaskFile,
  deleteTaskFile
};