-- 閱讀任務遊戲化系統資料庫初始化腳本

-- 建立資料庫（需要在PostgreSQL中手動執行）
-- CREATE DATABASE reading_game;
-- \c reading_game;

-- 建立使用者表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) CHECK (role IN ('student', 'teacher', 'admin')) DEFAULT 'student',
    class_name VARCHAR(50),
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 建立任務類型表
CREATE TABLE IF NOT EXISTS task_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    spirit_type VARCHAR(20),
    exp_reward INTEGER DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入任務類型數據
INSERT INTO task_types (type_code, name, description, spirit_type, exp_reward) VALUES
('summary', '課文心得', '閱讀課文後撰寫心得感想', 'reading', 15),
('opinion', '閱讀心得', '閱讀書籍或文章後分享個人看法', 'reading', 20),
('writing', '看圖寫作', '根據圖片內容進行創意寫作', 'writing', 25),
('dizigui', '弟子規', '學習弟子規內容並進行反思', 'etiquette', 15),
('calligraphy', '書法練習', '練習中文書法並上傳作品', 'writing', 10),
('book', '好書推薦', '推薦喜愛的書籍並說明理由', 'reading', 20),
('diary', '生活日記', '記錄日常生活點滴', 'writing', 15),
('etiquette', '生活禮儀', '學習並實踐日常禮儀', 'etiquette', 10),
('cleanup', '環境整理', '整理個人或共同空間', 'persistence', 10)
ON CONFLICT (type_code) DO NOTHING;

-- 建立任務表
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    task_type_id UUID REFERENCES task_types(id),
    student_id UUID REFERENCES users(id),
    teacher_id UUID REFERENCES users(id),
    content JSONB, -- 存儲任務內容（文字、圖片等）
    status VARCHAR(20) CHECK (status IN ('pending', 'submitted', 'approved', 'rejected')) DEFAULT 'pending',
    teacher_comment TEXT,
    teacher_rating INTEGER CHECK (teacher_rating >= 1 AND teacher_rating <= 5),
    submitted_at TIMESTAMP,
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 建立精靈類型表
CREATE TABLE IF NOT EXISTS spirit_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入精靈類型數據
INSERT INTO spirit_types (type_code, name, description, image_url) VALUES
('reading', '閱讀精靈', '熱愛閱讀，善於理解文字的智慧精靈', '/images/spirits/reading.png'),
('writing', '寫作精靈', '擅長文字創作，具有豐富想像力的精靈', '/images/spirits/writing.png'),
('etiquette', '禮儀精靈', '注重品德修養，懂得待人接物的優雅精靈', '/images/spirits/etiquette.png'),
('persistence', '堅持精靈', '具有恆心毅力，能夠持之以恆的堅韌精靈', '/images/spirits/persistence.png')
ON CONFLICT (type_code) DO NOTHING;

-- 建立使用者精靈表
CREATE TABLE IF NOT EXISTS user_spirits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    spirit_type_id UUID REFERENCES spirit_types(id),
    level INTEGER DEFAULT 1,
    current_exp INTEGER DEFAULT 0,
    total_exp INTEGER DEFAULT 0,
    acquired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, spirit_type_id)
);

-- 建立任務檔案表
CREATE TABLE IF NOT EXISTS task_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 建立系統設定表
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入系統預設設定
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('exp_per_level', '100', '每個等級所需經驗值'),
('max_spirit_level', '10', '精靈最高等級'),
('file_upload_limit', '10485760', '檔案上傳大小限制（位元組）'),
('supported_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', '支援的檔案類型')
ON CONFLICT (setting_key) DO NOTHING;

-- 建立索引
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_class ON users(class_name);
CREATE INDEX IF NOT EXISTS idx_tasks_student ON tasks(student_id);
CREATE INDEX IF NOT EXISTS idx_tasks_teacher ON tasks(teacher_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_type ON tasks(task_type_id);
CREATE INDEX IF NOT EXISTS idx_user_spirits_user ON user_spirits(user_id);
CREATE INDEX IF NOT EXISTS idx_task_files_task ON task_files(task_id);

-- 建立更新時間觸發器函數
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為需要的表建立更新時間觸發器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 建立預設管理員帳號（密碼需要加密）
-- INSERT INTO users (username, password_hash, name, role, email) VALUES
-- ('admin', '$2b$10$encrypted_password_here', '系統管理員', 'admin', '<EMAIL>');

COMMENT ON TABLE users IS '使用者表';
COMMENT ON TABLE task_types IS '任務類型表';
COMMENT ON TABLE tasks IS '任務表';
COMMENT ON TABLE spirit_types IS '精靈類型表';
COMMENT ON TABLE user_spirits IS '使用者精靈表';
COMMENT ON TABLE task_files IS '任務檔案表';
COMMENT ON TABLE system_settings IS '系統設定表';